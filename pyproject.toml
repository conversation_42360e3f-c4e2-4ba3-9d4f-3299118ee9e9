[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "dc_ai_red_line_review"
version = "1.3.6"
description = "A package for the Red Line Review project"
authors = [
  {name = "Adof<PERSON> Zhu"}
]
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
  "chonkie>=1.1.1",
  "openai>=1.79.0",
  "python-dotenv>=1.0.1",
  "rich>=13.9.4",
]



[tool.hatch.build]
packages = ["dc_ai_red_line_review"]  # Your source directory name
package-name = "dc_ai_red_line_review"  # The name when installed/imported
include = [
  "dc_ai_red_line_review/**",  # Update the include pattern
  "README.md"
]

[tool.ruff.format]
docstring-code-format = false
quote-style = "double"

[tool.ruff.lint]
extend-select = [
  "Q",
  "RUF100",
  "C90",
  "UP",
  "I",
  "D"
]
flake8-quotes = {inline-quotes = "double", multiline-quotes = "double"}
isort = {combine-as-imports = true}
mccabe = {max-complexity = 15}
ignore = [
  "D100", # ignore missing docstring in module
  "D101", # ignore missing docstring in public class
  "D102", # ignore missing docstring in public method
  "D104", # ignore missing docstring in public package
  "D105", # ignore missing docstring in magic methods
  "D107" # ignore missing docstring in __init__ methods
]

[tool.ruff.lint.pydocstyle]
convention = "google"

[dependency-groups]
dev = [
    "ruff>=0.11.9",
    "openpyxl>=3.1.5",
    "pandas>=2.3.1",
    "pycryptodome>=3.23.0",
    "mem0ai>=0.1.115",
    "opensearch-py>=3.0.0",
]
