import os

import httpx
from dotenv import load_dotenv
from openai import OpenAI

# 加载环境变量
load_dotenv(override=True)


def test_embedding_api():
    """测试embedding API是否可以正常访问."""
    print("🧪 测试Embedding API连接...")

    # 创建OpenAI客户端
    client = OpenAI(
        api_key=os.getenv("EMBEDDING_API_KEY"),
        base_url=os.getenv("EMBEDDING_BASE_URL"),
        http_client=httpx.Client(verify=False, timeout=30.0),
    )

    try:
        # 测试embedding调用
        response = client.embeddings.create(
            input=["测试文本"],
            model=os.getenv("EMBEDDING_MODEL"),
        )

        print("✅ Embedding API测试成功!")
        print(f"   模型: {response.model}")
        print(f"   向量维度: {len(response.data[0].embedding)}")
        print(f"   用量: {response.usage}")
        return True

    except Exception as e:
        print(f"❌ Embedding API测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    test_embedding_api()
