import os

import httpx
from dotenv import load_dotenv
from mem0 import Memory
from mem0.embeddings.openai import OpenAIEmbedding
from openai import OpenAI

# 加载环境变量
load_dotenv(override=True)


# Monkey patch OpenAIEmbedding to use insecure httpx client
original_init = OpenAIEmbedding.__init__


def patched_init(self, config=None):
    original_init(self, config)
    # 对于Qwen3-Embedding-0.6B，不设置embedding_dims
    if "Qwen3-Embedding" in self.config.model:
        self.config.embedding_dims = None
    # 重新创建客户端，使用不验证SSL的httpx客户端
    http_client = httpx.Client(verify=False, timeout=30.0)
    self.client = OpenAI(
        api_key=self.client.api_key,
        base_url=self.client.base_url,
        http_client=http_client,
    )


# Monkey patch embed method to handle None embedding_dims
original_embed = OpenAIEmbedding.embed


def patched_embed(self, text, memory_action=None):
    text = text.replace("\n", " ")
    # 如果embedding_dims为None，不传递dimensions参数
    if self.config.embedding_dims is None:
        return (
            self.client.embeddings.create(input=[text], model=self.config.model)
            .data[0]
            .embedding
        )
    else:
        return (
            self.client.embeddings.create(
                input=[text],
                model=self.config.model,
                dimensions=self.config.embedding_dims,
            )
            .data[0]
            .embedding
        )


OpenAIEmbedding.__init__ = patched_init
OpenAIEmbedding.embed = patched_embed


def create_memory_instance():
    """创建mem0实例，使用环境变量配置."""
    config = {
        "llm": {
            "provider": "openai",
            "config": {
                "model": os.getenv("MODEL_NAME"),
                "api_key": os.getenv("QWQ_API_KEY"),
                "openai_base_url": os.getenv("QWQ_BASE_URL"),
                "temperature": 0.1,
                "max_tokens": 1500,
            },
        },
        "embedder": {
            "provider": "openai",
            "config": {
                "model": os.getenv("EMBEDDING_MODEL"),
                "api_key": os.getenv("EMBEDDING_API_KEY"),
                "openai_base_url": os.getenv("EMBEDDING_BASE_URL"),
                # 不设置embedding_dims，让模型自动决定
                # 对于Qwen3-Embedding-0.6B，不支持自定义维度
            },
        },
        "vector_store": {
            "provider": "opensearch",
            "config": {
                "host": os.getenv("OPENSEARCH_HOST"),
                "port": int(os.getenv("OPENSEARCH_PORT", "443")),
                "http_auth": (
                    os.getenv("OPENSEARCH_USERNAME"),
                    os.getenv("OPENSEARCH_PASSWORD"),
                ),
                "embedding_model_dims": 1024,
                "pool_maxsize": 20,
                "verify_certs": True,
                "use_ssl": True,
                "collection_name": os.getenv("OPENSEARCH_INDEX", "test_mem0"),
            },
        },
        "version": "v1.1",
    }

    return Memory.from_config(config)


def demo():
    """演示mem0基本功能."""
    print("🚀 Mem0 + OpenSearch 最佳实践演示")
    print("=" * 50)

    # 创建memory实例
    try:
        memory = create_memory_instance()
        print("✅ Mem0初始化成功")
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return

    user_id = "test_user"

    # 添加记忆（直接模式，不需要LLM推理）
    print("\n📝 添加记忆...")
    try:
        result = memory.add(
            "用户是风控专家，专门分析加密货币交易风险",
            user_id=user_id,
            infer=False,  # 直接添加，避免LLM处理
        )
        mem_id = result["results"][0]["id"]
        print(f"   ✅ 添加成功: {mem_id}")
    except Exception as e:
        print(f"   ❌ 添加失败: {e}")
        print(f"   详细错误信息: {type(e).__name__}: {str(e)}")
        import traceback

        traceback.print_exc()
        return

    # 搜索记忆
    print("\n🔍 搜索记忆...")
    try:
        result = memory.search("你是谁", user_id=user_id, limit=3)
        memories = result.get("results", [])

        if memories:
            for i, mem in enumerate(memories, 1):
                print(
                    f"   {i}. {mem['memory'][:50]}... (相关度: {mem.get('score', 'N/A')})"
                )
        else:
            print("   未找到相关记忆")
    except Exception as e:
        print(f"   ❌ 搜索失败: {e}")

    # 获取所有记忆
    print("\n📋 用户记忆总数...")
    try:
        all_memories = memory.get_all(user_id=user_id)
        print(f"   总共: {len(all_memories)} 条")
    except Exception as e:
        print(f"   ❌ 获取失败: {e}")

    # 清理测试数据
    print("\n🗑️ 清理测试数据...")
    try:
        memory.delete(mem_id)
        print("   ✅ 清理完成")
    except Exception as e:
        print(f"   ❌ 清理失败: {e}")

    print("\n🎉 演示完成!")


if __name__ == "__main__":
    demo()
