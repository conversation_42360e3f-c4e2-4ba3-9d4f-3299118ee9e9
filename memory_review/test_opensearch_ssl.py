#!/usr/bin/env python3
"""
测试OpenSearch SSL连接配置
验证不同SSL设置下的连接情况
"""

import os
import ssl
import socket
from urllib.parse import urlparse

import httpx
from dotenv import load_dotenv
from opensearchpy import OpenSearch

# 加载环境变量
load_dotenv(override=True)


def test_ssl_connection():
    """测试SSL连接的基本信息."""
    host = os.getenv("OPENSEARCH_HOST")
    port = int(os.getenv("OPENSEARCH_PORT", "443"))
    
    print(f"🔍 测试SSL连接到 {host}:{port}")
    print("=" * 50)
    
    try:
        # 创建SSL上下文
        context = ssl.create_default_context()
        
        # 测试1: 标准SSL验证
        print("\n1️⃣ 测试标准SSL证书验证...")
        try:
            with socket.create_connection((host, port), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=host) as ssock:
                    cert = ssock.getpeercert()
                    print(f"   ✅ SSL连接成功")
                    print(f"   📜 证书主题: {cert.get('subject', 'N/A')}")
                    print(f"   🏢 证书颁发者: {cert.get('issuer', 'N/A')}")
                    print(f"   📅 有效期: {cert.get('notBefore', 'N/A')} - {cert.get('notAfter', 'N/A')}")
        except ssl.SSLError as e:
            print(f"   ❌ SSL验证失败: {e}")
        except Exception as e:
            print(f"   ❌ 连接失败: {e}")
        
        # 测试2: 忽略SSL验证
        print("\n2️⃣ 测试忽略SSL证书验证...")
        try:
            context_no_verify = ssl.create_default_context()
            context_no_verify.check_hostname = False
            context_no_verify.verify_mode = ssl.CERT_NONE
            
            with socket.create_connection((host, port), timeout=10) as sock:
                with context_no_verify.wrap_socket(sock, server_hostname=host) as ssock:
                    print(f"   ✅ 忽略SSL验证连接成功")
        except Exception as e:
            print(f"   ❌ 连接失败: {e}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")


def test_opensearch_connection_with_ssl():
    """测试OpenSearch客户端的SSL连接."""
    print("\n🔗 测试OpenSearch客户端连接")
    print("=" * 50)
    
    host = os.getenv("OPENSEARCH_HOST")
    port = int(os.getenv("OPENSEARCH_PORT", "443"))
    username = os.getenv("OPENSEARCH_USERNAME")
    password = os.getenv("OPENSEARCH_PASSWORD")
    
    # 测试1: verify_certs=True
    print("\n1️⃣ 测试 verify_certs=True...")
    try:
        client = OpenSearch(
            hosts=[{"host": host, "port": port}],
            http_auth=(username, password),
            use_ssl=True,
            verify_certs=True,
            ssl_show_warn=False,
            timeout=10,
        )
        
        info = client.info()
        print(f"   ✅ 连接成功: {info.get('version', {}).get('number', 'Unknown')}")
        
    except Exception as e:
        print(f"   ❌ 连接失败: {e}")
    
    # 测试2: verify_certs=False
    print("\n2️⃣ 测试 verify_certs=False...")
    try:
        client = OpenSearch(
            hosts=[{"host": host, "port": port}],
            http_auth=(username, password),
            use_ssl=True,
            verify_certs=False,
            ssl_show_warn=False,
            timeout=10,
        )
        
        info = client.info()
        print(f"   ✅ 连接成功: {info.get('version', {}).get('number', 'Unknown')}")
        
    except Exception as e:
        print(f"   ❌ 连接失败: {e}")


def test_httpx_ssl():
    """测试httpx客户端的SSL连接."""
    print("\n🌐 测试HTTPX SSL连接")
    print("=" * 50)
    
    host = os.getenv("OPENSEARCH_HOST")
    port = int(os.getenv("OPENSEARCH_PORT", "443"))
    username = os.getenv("OPENSEARCH_USERNAME")
    password = os.getenv("OPENSEARCH_PASSWORD")
    
    base_url = f"https://{host}:{port}"
    
    # 测试1: 验证SSL
    print("\n1️⃣ 测试HTTPX verify=True...")
    try:
        with httpx.Client(verify=True, timeout=10.0) as client:
            response = client.get(
                f"{base_url}/",
                auth=(username, password)
            )
            print(f"   ✅ 连接成功: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 连接失败: {e}")
    
    # 测试2: 忽略SSL验证
    print("\n2️⃣ 测试HTTPX verify=False...")
    try:
        with httpx.Client(verify=False, timeout=10.0) as client:
            response = client.get(
                f"{base_url}/",
                auth=(username, password)
            )
            print(f"   ✅ 连接成功: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 连接失败: {e}")


def analyze_ssl_issue():
    """分析SSL问题的可能原因."""
    print("\n🔍 SSL问题分析")
    print("=" * 50)
    
    host = os.getenv("OPENSEARCH_HOST")
    
    print(f"目标主机: {host}")
    print("\n可能的SSL问题原因:")
    print("1. 🔒 自签名证书 - 开发环境常用自签名证书")
    print("2. 🏷️  证书域名不匹配 - 证书可能是为其他域名签发的")
    print("3. 📅 证书过期 - SSL证书可能已经过期")
    print("4. 🔗 证书链不完整 - 缺少中间证书")
    print("5. 🏢 内部CA - 使用了企业内部CA，系统不信任")
    
    print("\n💡 解决方案:")
    print("1. 开发环境: 使用 verify_certs=False")
    print("2. 生产环境: 配置正确的CA证书路径")
    print("3. 企业环境: 安装企业CA证书到系统信任库")
    print("4. 临时方案: 使用IP地址而非域名（如果支持）")


def main():
    """主函数."""
    print("🚀 OpenSearch SSL连接诊断工具")
    print("=" * 60)
    
    # 基本SSL连接测试
    test_ssl_connection()
    
    # OpenSearch客户端测试
    test_opensearch_connection_with_ssl()
    
    # HTTPX客户端测试
    test_httpx_ssl()
    
    # 问题分析
    analyze_ssl_issue()
    
    print("\n🎯 建议:")
    print("- 开发环境: 设置 OPENSEARCH_VERIFY_CERTS=false")
    print("- 生产环境: 获取正确的CA证书并配置 OPENSEARCH_CA_CERTS")
    print("- 如果是内部域名，考虑添加到系统hosts文件或使用IP地址")


if __name__ == "__main__":
    main()
